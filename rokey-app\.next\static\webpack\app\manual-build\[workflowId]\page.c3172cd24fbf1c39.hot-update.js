"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useWorkflowWebSocket.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useWorkflowWebSocket.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWorkflowWebSocket: () => (/* binding */ useWorkflowWebSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * React Hook for WebSocket workflow updates\n * Provides real-time updates for Manual Build workflows\n */ \nfunction useWorkflowWebSocket(workflowId) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { autoConnect = true, maxEvents = 100, reconnectInterval = 5000, onEvent, onConnect, onDisconnect, onError } = options;\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        isConnected: false,\n        isConnecting: false,\n        error: null,\n        lastEvent: null,\n        events: [],\n        connectionCount: 0\n    });\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const isManualDisconnectRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isConnectedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isConnectingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Connect to WebSocket\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[connect]\": ()=>{\n            // Use refs to check current state instead of state dependencies\n            if (!workflowId || isConnectedRef.current || isConnectingRef.current) {\n                return;\n            }\n            isConnectingRef.current = true;\n            setState({\n                \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>({\n                        ...prev,\n                        isConnecting: true,\n                        error: null\n                    })\n            }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n            isManualDisconnectRef.current = false;\n            try {\n                const eventSource = new EventSource(\"/api/workflow/stream/\".concat(workflowId));\n                eventSourceRef.current = eventSource;\n                eventSource.onopen = ({\n                    \"useWorkflowWebSocket.useCallback[connect]\": ()=>{\n                        console.log(\"[Workflow WebSocket] Connected to workflow \".concat(workflowId));\n                        isConnectedRef.current = true;\n                        isConnectingRef.current = false;\n                        setState({\n                            \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>({\n                                    ...prev,\n                                    isConnected: true,\n                                    isConnecting: false,\n                                    error: null,\n                                    connectionCount: prev.connectionCount + 1\n                                })\n                        }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n                        onConnect === null || onConnect === void 0 ? void 0 : onConnect();\n                    }\n                })[\"useWorkflowWebSocket.useCallback[connect]\"];\n                eventSource.onmessage = ({\n                    \"useWorkflowWebSocket.useCallback[connect]\": (event)=>{\n                        try {\n                            const workflowEvent = JSON.parse(event.data);\n                            setState({\n                                \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>{\n                                    const newEvents = [\n                                        ...prev.events,\n                                        workflowEvent\n                                    ];\n                                    // Keep only the last maxEvents\n                                    if (newEvents.length > maxEvents) {\n                                        newEvents.splice(0, newEvents.length - maxEvents);\n                                    }\n                                    return {\n                                        ...prev,\n                                        lastEvent: workflowEvent,\n                                        events: newEvents\n                                    };\n                                }\n                            }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n                            onEvent === null || onEvent === void 0 ? void 0 : onEvent(workflowEvent);\n                            console.log(\"[Workflow WebSocket] Received event:\", workflowEvent);\n                        } catch (error) {\n                            console.error('[Workflow WebSocket] Failed to parse event:', error);\n                        }\n                    }\n                })[\"useWorkflowWebSocket.useCallback[connect]\"];\n                eventSource.onerror = ({\n                    \"useWorkflowWebSocket.useCallback[connect]\": (error)=>{\n                        console.error(\"[Workflow WebSocket] Connection error:\", error);\n                        isConnectedRef.current = false;\n                        isConnectingRef.current = false;\n                        setState({\n                            \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>({\n                                    ...prev,\n                                    isConnected: false,\n                                    isConnecting: false,\n                                    error: 'Connection error'\n                                })\n                        }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n                        onError === null || onError === void 0 ? void 0 : onError('Connection error');\n                        // Auto-reconnect if not manually disconnected\n                        if (!isManualDisconnectRef.current && reconnectInterval > 0) {\n                            reconnectTimeoutRef.current = setTimeout({\n                                \"useWorkflowWebSocket.useCallback[connect]\": ()=>{\n                                    console.log(\"[Workflow WebSocket] Attempting to reconnect...\");\n                                    connect();\n                                }\n                            }[\"useWorkflowWebSocket.useCallback[connect]\"], reconnectInterval);\n                        }\n                    }\n                })[\"useWorkflowWebSocket.useCallback[connect]\"];\n            } catch (error) {\n                console.error('[Workflow WebSocket] Failed to create connection:', error);\n                isConnectingRef.current = false;\n                setState({\n                    \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>({\n                            ...prev,\n                            isConnecting: false,\n                            error: 'Failed to create connection'\n                        })\n                }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n                onError === null || onError === void 0 ? void 0 : onError('Failed to create connection');\n            }\n        }\n    }[\"useWorkflowWebSocket.useCallback[connect]\"], [\n        workflowId,\n        maxEvents,\n        reconnectInterval,\n        onConnect,\n        onEvent,\n        onError\n    ]);\n    // Disconnect from WebSocket\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[disconnect]\": ()=>{\n            isManualDisconnectRef.current = true;\n            if (reconnectTimeoutRef.current) {\n                clearTimeout(reconnectTimeoutRef.current);\n                reconnectTimeoutRef.current = null;\n            }\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n                eventSourceRef.current = null;\n            }\n            setState({\n                \"useWorkflowWebSocket.useCallback[disconnect]\": (prev)=>({\n                        ...prev,\n                        isConnected: false,\n                        isConnecting: false,\n                        error: null\n                    })\n            }[\"useWorkflowWebSocket.useCallback[disconnect]\"]);\n            onDisconnect === null || onDisconnect === void 0 ? void 0 : onDisconnect();\n            console.log(\"[Workflow WebSocket] Disconnected from workflow \".concat(workflowId));\n        // Using workflowId from closure, not as dependency\n        }\n    }[\"useWorkflowWebSocket.useCallback[disconnect]\"], [\n        onDisconnect\n    ]);\n    // Send event to workflow\n    const sendEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[sendEvent]\": async (eventType, data, executionId)=>{\n            if (!workflowId) {\n                throw new Error('No workflow ID provided');\n            }\n            try {\n                const response = await fetch(\"/api/workflow/stream/\".concat(workflowId), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        eventType,\n                        data,\n                        executionId\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to send event: \".concat(response.statusText));\n                }\n                console.log(\"[Workflow WebSocket] Sent event \".concat(eventType, \" to workflow \").concat(workflowId));\n            } catch (error) {\n                console.error('[Workflow WebSocket] Failed to send event:', error);\n                throw error;\n            }\n        }\n    }[\"useWorkflowWebSocket.useCallback[sendEvent]\"], [\n        workflowId\n    ]);\n    // Clear events history\n    const clearEvents = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[clearEvents]\": ()=>{\n            setState({\n                \"useWorkflowWebSocket.useCallback[clearEvents]\": (prev)=>({\n                        ...prev,\n                        events: [],\n                        lastEvent: null\n                    })\n            }[\"useWorkflowWebSocket.useCallback[clearEvents]\"]);\n        }\n    }[\"useWorkflowWebSocket.useCallback[clearEvents]\"], []);\n    // Reconnect\n    const reconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[reconnect]\": ()=>{\n            disconnect();\n            setTimeout(connect, 100);\n        }\n    }[\"useWorkflowWebSocket.useCallback[reconnect]\"], [\n        disconnect,\n        connect\n    ]);\n    // Auto-connect on mount or workflowId change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWorkflowWebSocket.useEffect\": ()=>{\n            if (autoConnect && workflowId) {\n                // Use a timeout to avoid immediate state checks that could cause loops\n                const timeoutId = setTimeout({\n                    \"useWorkflowWebSocket.useEffect.timeoutId\": ()=>{\n                        setState({\n                            \"useWorkflowWebSocket.useEffect.timeoutId\": (prev)=>{\n                                if (!prev.isConnected && !prev.isConnecting) {\n                                    connect();\n                                }\n                                return prev;\n                            }\n                        }[\"useWorkflowWebSocket.useEffect.timeoutId\"]);\n                    }\n                }[\"useWorkflowWebSocket.useEffect.timeoutId\"], 0);\n                return ({\n                    \"useWorkflowWebSocket.useEffect\": ()=>{\n                        clearTimeout(timeoutId);\n                        if (reconnectTimeoutRef.current) {\n                            clearTimeout(reconnectTimeoutRef.current);\n                        }\n                    }\n                })[\"useWorkflowWebSocket.useEffect\"];\n            }\n            return ({\n                \"useWorkflowWebSocket.useEffect\": ()=>{\n                    if (reconnectTimeoutRef.current) {\n                        clearTimeout(reconnectTimeoutRef.current);\n                    }\n                }\n            })[\"useWorkflowWebSocket.useEffect\"];\n        }\n    }[\"useWorkflowWebSocket.useEffect\"], [\n        workflowId,\n        autoConnect,\n        connect\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWorkflowWebSocket.useEffect\": ()=>{\n            return ({\n                \"useWorkflowWebSocket.useEffect\": ()=>{\n                    disconnect();\n                }\n            })[\"useWorkflowWebSocket.useEffect\"];\n        }\n    }[\"useWorkflowWebSocket.useEffect\"], [\n        disconnect\n    ]);\n    const actions = {\n        connect,\n        disconnect,\n        sendEvent,\n        clearEvents,\n        reconnect\n    };\n    return [\n        state,\n        actions\n    ];\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9ob29rcy91c2VXb3JrZmxvd1dlYlNvY2tldC50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTs7O0NBR0MsR0FFZ0U7QUFvQjFELFNBQVNJLHFCQUNkQyxVQUF5QjtRQUN6QkMsVUFBQUEsaUVBUUksQ0FBQztJQUVMLE1BQU0sRUFDSkMsY0FBYyxJQUFJLEVBQ2xCQyxZQUFZLEdBQUcsRUFDZkMsb0JBQW9CLElBQUksRUFDeEJDLE9BQU8sRUFDUEMsU0FBUyxFQUNUQyxZQUFZLEVBQ1pDLE9BQU8sRUFDUixHQUFHUDtJQUVKLE1BQU0sQ0FBQ1EsT0FBT0MsU0FBUyxHQUFHYiwrQ0FBUUEsQ0FBeUI7UUFDekRjLGFBQWE7UUFDYkMsY0FBYztRQUNkQyxPQUFPO1FBQ1BDLFdBQVc7UUFDWEMsUUFBUSxFQUFFO1FBQ1ZDLGlCQUFpQjtJQUNuQjtJQUVBLE1BQU1DLGlCQUFpQnJCLDZDQUFNQSxDQUFxQjtJQUNsRCxNQUFNc0Isc0JBQXNCdEIsNkNBQU1BLENBQXdCO0lBQzFELE1BQU11Qix3QkFBd0J2Qiw2Q0FBTUEsQ0FBQztJQUNyQyxNQUFNd0IsaUJBQWlCeEIsNkNBQU1BLENBQUM7SUFDOUIsTUFBTXlCLGtCQUFrQnpCLDZDQUFNQSxDQUFDO0lBRS9CLHVCQUF1QjtJQUN2QixNQUFNMEIsVUFBVXhCLGtEQUFXQTtxREFBQztZQUMxQixnRUFBZ0U7WUFDaEUsSUFBSSxDQUFDRSxjQUFjb0IsZUFBZUcsT0FBTyxJQUFJRixnQkFBZ0JFLE9BQU8sRUFBRTtnQkFDcEU7WUFDRjtZQUVBRixnQkFBZ0JFLE9BQU8sR0FBRztZQUMxQmI7NkRBQVNjLENBQUFBLE9BQVM7d0JBQUUsR0FBR0EsSUFBSTt3QkFBRVosY0FBYzt3QkFBTUMsT0FBTztvQkFBSzs7WUFDN0RNLHNCQUFzQkksT0FBTyxHQUFHO1lBRWhDLElBQUk7Z0JBQ0YsTUFBTUUsY0FBYyxJQUFJQyxZQUFZLHdCQUFtQyxPQUFYMUI7Z0JBQzVEaUIsZUFBZU0sT0FBTyxHQUFHRTtnQkFFekJBLFlBQVlFLE1BQU07aUVBQUc7d0JBQ25CQyxRQUFRQyxHQUFHLENBQUMsOENBQXlELE9BQVg3Qjt3QkFDMURvQixlQUFlRyxPQUFPLEdBQUc7d0JBQ3pCRixnQkFBZ0JFLE9BQU8sR0FBRzt3QkFDMUJiO3lFQUFTYyxDQUFBQSxPQUFTO29DQUNoQixHQUFHQSxJQUFJO29DQUNQYixhQUFhO29DQUNiQyxjQUFjO29DQUNkQyxPQUFPO29DQUNQRyxpQkFBaUJRLEtBQUtSLGVBQWUsR0FBRztnQ0FDMUM7O3dCQUNBVixzQkFBQUEsZ0NBQUFBO29CQUNGOztnQkFFQW1CLFlBQVlLLFNBQVM7aUVBQUcsQ0FBQ0M7d0JBQ3ZCLElBQUk7NEJBQ0YsTUFBTUMsZ0JBQXdDQyxLQUFLQyxLQUFLLENBQUNILE1BQU1JLElBQUk7NEJBRW5FekI7NkVBQVNjLENBQUFBO29DQUNQLE1BQU1ZLFlBQVk7MkNBQUlaLEtBQUtULE1BQU07d0NBQUVpQjtxQ0FBYztvQ0FDakQsK0JBQStCO29DQUMvQixJQUFJSSxVQUFVQyxNQUFNLEdBQUdsQyxXQUFXO3dDQUNoQ2lDLFVBQVVFLE1BQU0sQ0FBQyxHQUFHRixVQUFVQyxNQUFNLEdBQUdsQztvQ0FDekM7b0NBRUEsT0FBTzt3Q0FDTCxHQUFHcUIsSUFBSTt3Q0FDUFYsV0FBV2tCO3dDQUNYakIsUUFBUXFCO29DQUNWO2dDQUNGOzs0QkFFQS9CLG9CQUFBQSw4QkFBQUEsUUFBVTJCOzRCQUNWSixRQUFRQyxHQUFHLENBQUUsd0NBQXVDRzt3QkFDdEQsRUFBRSxPQUFPbkIsT0FBTzs0QkFDZGUsUUFBUWYsS0FBSyxDQUFDLCtDQUErQ0E7d0JBQy9EO29CQUNGOztnQkFFQVksWUFBWWMsT0FBTztpRUFBRyxDQUFDMUI7d0JBQ3JCZSxRQUFRZixLQUFLLENBQUUsMENBQXlDQTt3QkFFeERPLGVBQWVHLE9BQU8sR0FBRzt3QkFDekJGLGdCQUFnQkUsT0FBTyxHQUFHO3dCQUMxQmI7eUVBQVNjLENBQUFBLE9BQVM7b0NBQ2hCLEdBQUdBLElBQUk7b0NBQ1BiLGFBQWE7b0NBQ2JDLGNBQWM7b0NBQ2RDLE9BQU87Z0NBQ1Q7O3dCQUVBTCxvQkFBQUEsOEJBQUFBLFFBQVU7d0JBRVYsOENBQThDO3dCQUM5QyxJQUFJLENBQUNXLHNCQUFzQkksT0FBTyxJQUFJbkIsb0JBQW9CLEdBQUc7NEJBQzNEYyxvQkFBb0JLLE9BQU8sR0FBR2lCOzZFQUFXO29DQUN2Q1osUUFBUUMsR0FBRyxDQUFFO29DQUNiUDtnQ0FDRjs0RUFBR2xCO3dCQUNMO29CQUNGOztZQUVGLEVBQUUsT0FBT1MsT0FBTztnQkFDZGUsUUFBUWYsS0FBSyxDQUFDLHFEQUFxREE7Z0JBQ25FUSxnQkFBZ0JFLE9BQU8sR0FBRztnQkFDMUJiO2lFQUFTYyxDQUFBQSxPQUFTOzRCQUNoQixHQUFHQSxJQUFJOzRCQUNQWixjQUFjOzRCQUNkQyxPQUFPO3dCQUNUOztnQkFDQUwsb0JBQUFBLDhCQUFBQSxRQUFVO1lBQ1o7UUFDRjtvREFBRztRQUFDUjtRQUFZRztRQUFXQztRQUFtQkU7UUFBV0Q7UUFBU0c7S0FBUTtJQUUxRSw0QkFBNEI7SUFDNUIsTUFBTWlDLGFBQWEzQyxrREFBV0E7d0RBQUM7WUFDN0JxQixzQkFBc0JJLE9BQU8sR0FBRztZQUVoQyxJQUFJTCxvQkFBb0JLLE9BQU8sRUFBRTtnQkFDL0JtQixhQUFheEIsb0JBQW9CSyxPQUFPO2dCQUN4Q0wsb0JBQW9CSyxPQUFPLEdBQUc7WUFDaEM7WUFFQSxJQUFJTixlQUFlTSxPQUFPLEVBQUU7Z0JBQzFCTixlQUFlTSxPQUFPLENBQUNvQixLQUFLO2dCQUM1QjFCLGVBQWVNLE9BQU8sR0FBRztZQUMzQjtZQUVBYjtnRUFBU2MsQ0FBQUEsT0FBUzt3QkFDaEIsR0FBR0EsSUFBSTt3QkFDUGIsYUFBYTt3QkFDYkMsY0FBYzt3QkFDZEMsT0FBTztvQkFDVDs7WUFFQU4seUJBQUFBLG1DQUFBQTtZQUNBcUIsUUFBUUMsR0FBRyxDQUFDLG1EQUE4RCxPQUFYN0I7UUFDL0QsbURBQW1EO1FBQ3JEO3VEQUFHO1FBQUNPO0tBQWE7SUFFakIseUJBQXlCO0lBQ3pCLE1BQU1xQyxZQUFZOUMsa0RBQVdBO3VEQUFDLE9BQzVCK0MsV0FDQVYsTUFDQVc7WUFFQSxJQUFJLENBQUM5QyxZQUFZO2dCQUNmLE1BQU0sSUFBSStDLE1BQU07WUFDbEI7WUFFQSxJQUFJO2dCQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTSx3QkFBbUMsT0FBWGpELGFBQWM7b0JBQ2pFa0QsUUFBUTtvQkFDUkMsU0FBUzt3QkFDUCxnQkFBZ0I7b0JBQ2xCO29CQUNBQyxNQUFNbkIsS0FBS29CLFNBQVMsQ0FBQzt3QkFDbkJSO3dCQUNBVjt3QkFDQVc7b0JBQ0Y7Z0JBQ0Y7Z0JBRUEsSUFBSSxDQUFDRSxTQUFTTSxFQUFFLEVBQUU7b0JBQ2hCLE1BQU0sSUFBSVAsTUFBTSx5QkFBNkMsT0FBcEJDLFNBQVNPLFVBQVU7Z0JBQzlEO2dCQUVBM0IsUUFBUUMsR0FBRyxDQUFDLG1DQUE0RDdCLE9BQXpCNkMsV0FBVSxpQkFBMEIsT0FBWDdDO1lBQzFFLEVBQUUsT0FBT2EsT0FBTztnQkFDZGUsUUFBUWYsS0FBSyxDQUFDLDhDQUE4Q0E7Z0JBQzVELE1BQU1BO1lBQ1I7UUFDRjtzREFBRztRQUFDYjtLQUFXO0lBRWYsdUJBQXVCO0lBQ3ZCLE1BQU13RCxjQUFjMUQsa0RBQVdBO3lEQUFDO1lBQzlCWTtpRUFBU2MsQ0FBQUEsT0FBUzt3QkFDaEIsR0FBR0EsSUFBSTt3QkFDUFQsUUFBUSxFQUFFO3dCQUNWRCxXQUFXO29CQUNiOztRQUNGO3dEQUFHLEVBQUU7SUFFTCxZQUFZO0lBQ1osTUFBTTJDLFlBQVkzRCxrREFBV0E7dURBQUM7WUFDNUIyQztZQUNBRCxXQUFXbEIsU0FBUztRQUN0QjtzREFBRztRQUFDbUI7UUFBWW5CO0tBQVE7SUFFeEIsNkNBQTZDO0lBQzdDM0IsZ0RBQVNBOzBDQUFDO1lBQ1IsSUFBSU8sZUFBZUYsWUFBWTtnQkFDN0IsdUVBQXVFO2dCQUN2RSxNQUFNMEQsWUFBWWxCO2dFQUFXO3dCQUMzQjlCO3dFQUFTYyxDQUFBQTtnQ0FDUCxJQUFJLENBQUNBLEtBQUtiLFdBQVcsSUFBSSxDQUFDYSxLQUFLWixZQUFZLEVBQUU7b0NBQzNDVTtnQ0FDRjtnQ0FDQSxPQUFPRTs0QkFDVDs7b0JBQ0Y7K0RBQUc7Z0JBRUg7c0RBQU87d0JBQ0xrQixhQUFhZ0I7d0JBQ2IsSUFBSXhDLG9CQUFvQkssT0FBTyxFQUFFOzRCQUMvQm1CLGFBQWF4QixvQkFBb0JLLE9BQU87d0JBQzFDO29CQUNGOztZQUNGO1lBRUE7a0RBQU87b0JBQ0wsSUFBSUwsb0JBQW9CSyxPQUFPLEVBQUU7d0JBQy9CbUIsYUFBYXhCLG9CQUFvQkssT0FBTztvQkFDMUM7Z0JBQ0Y7O1FBQ0Y7eUNBQUc7UUFBQ3ZCO1FBQVlFO1FBQWFvQjtLQUFRO0lBRXJDLHFCQUFxQjtJQUNyQjNCLGdEQUFTQTswQ0FBQztZQUNSO2tEQUFPO29CQUNMOEM7Z0JBQ0Y7O1FBQ0Y7eUNBQUc7UUFBQ0E7S0FBVztJQUVmLE1BQU1rQixVQUFvQztRQUN4Q3JDO1FBQ0FtQjtRQUNBRztRQUNBWTtRQUNBQztJQUNGO0lBRUEsT0FBTztRQUFDaEQ7UUFBT2tEO0tBQVE7QUFDekIiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcc3JjXFxob29rc1xcdXNlV29ya2Zsb3dXZWJTb2NrZXQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBSZWFjdCBIb29rIGZvciBXZWJTb2NrZXQgd29ya2Zsb3cgdXBkYXRlc1xuICogUHJvdmlkZXMgcmVhbC10aW1lIHVwZGF0ZXMgZm9yIE1hbnVhbCBCdWlsZCB3b3JrZmxvd3NcbiAqL1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgV29ya2Zsb3dXZWJTb2NrZXRFdmVudCB9IGZyb20gJ0AvbGliL3dlYnNvY2tldC9Xb3JrZmxvd1dlYlNvY2tldFNlcnZlcic7XG5cbmV4cG9ydCBpbnRlcmZhY2UgV29ya2Zsb3dXZWJTb2NrZXRTdGF0ZSB7XG4gIGlzQ29ubmVjdGVkOiBib29sZWFuO1xuICBpc0Nvbm5lY3Rpbmc6IGJvb2xlYW47XG4gIGVycm9yOiBzdHJpbmcgfCBudWxsO1xuICBsYXN0RXZlbnQ6IFdvcmtmbG93V2ViU29ja2V0RXZlbnQgfCBudWxsO1xuICBldmVudHM6IFdvcmtmbG93V2ViU29ja2V0RXZlbnRbXTtcbiAgY29ubmVjdGlvbkNvdW50OiBudW1iZXI7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgV29ya2Zsb3dXZWJTb2NrZXRBY3Rpb25zIHtcbiAgY29ubmVjdDogKCkgPT4gdm9pZDtcbiAgZGlzY29ubmVjdDogKCkgPT4gdm9pZDtcbiAgc2VuZEV2ZW50OiAoZXZlbnRUeXBlOiBzdHJpbmcsIGRhdGE6IGFueSwgZXhlY3V0aW9uSWQ/OiBzdHJpbmcpID0+IFByb21pc2U8dm9pZD47XG4gIGNsZWFyRXZlbnRzOiAoKSA9PiB2b2lkO1xuICByZWNvbm5lY3Q6ICgpID0+IHZvaWQ7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiB1c2VXb3JrZmxvd1dlYlNvY2tldChcbiAgd29ya2Zsb3dJZDogc3RyaW5nIHwgbnVsbCxcbiAgb3B0aW9uczoge1xuICAgIGF1dG9Db25uZWN0PzogYm9vbGVhbjtcbiAgICBtYXhFdmVudHM/OiBudW1iZXI7XG4gICAgcmVjb25uZWN0SW50ZXJ2YWw/OiBudW1iZXI7XG4gICAgb25FdmVudD86IChldmVudDogV29ya2Zsb3dXZWJTb2NrZXRFdmVudCkgPT4gdm9pZDtcbiAgICBvbkNvbm5lY3Q/OiAoKSA9PiB2b2lkO1xuICAgIG9uRGlzY29ubmVjdD86ICgpID0+IHZvaWQ7XG4gICAgb25FcnJvcj86IChlcnJvcjogc3RyaW5nKSA9PiB2b2lkO1xuICB9ID0ge31cbik6IFtXb3JrZmxvd1dlYlNvY2tldFN0YXRlLCBXb3JrZmxvd1dlYlNvY2tldEFjdGlvbnNdIHtcbiAgY29uc3Qge1xuICAgIGF1dG9Db25uZWN0ID0gdHJ1ZSxcbiAgICBtYXhFdmVudHMgPSAxMDAsXG4gICAgcmVjb25uZWN0SW50ZXJ2YWwgPSA1MDAwLFxuICAgIG9uRXZlbnQsXG4gICAgb25Db25uZWN0LFxuICAgIG9uRGlzY29ubmVjdCxcbiAgICBvbkVycm9yXG4gIH0gPSBvcHRpb25zO1xuXG4gIGNvbnN0IFtzdGF0ZSwgc2V0U3RhdGVdID0gdXNlU3RhdGU8V29ya2Zsb3dXZWJTb2NrZXRTdGF0ZT4oe1xuICAgIGlzQ29ubmVjdGVkOiBmYWxzZSxcbiAgICBpc0Nvbm5lY3Rpbmc6IGZhbHNlLFxuICAgIGVycm9yOiBudWxsLFxuICAgIGxhc3RFdmVudDogbnVsbCxcbiAgICBldmVudHM6IFtdLFxuICAgIGNvbm5lY3Rpb25Db3VudDogMFxuICB9KTtcblxuICBjb25zdCBldmVudFNvdXJjZVJlZiA9IHVzZVJlZjxFdmVudFNvdXJjZSB8IG51bGw+KG51bGwpO1xuICBjb25zdCByZWNvbm5lY3RUaW1lb3V0UmVmID0gdXNlUmVmPE5vZGVKUy5UaW1lb3V0IHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IGlzTWFudWFsRGlzY29ubmVjdFJlZiA9IHVzZVJlZihmYWxzZSk7XG4gIGNvbnN0IGlzQ29ubmVjdGVkUmVmID0gdXNlUmVmKGZhbHNlKTtcbiAgY29uc3QgaXNDb25uZWN0aW5nUmVmID0gdXNlUmVmKGZhbHNlKTtcblxuICAvLyBDb25uZWN0IHRvIFdlYlNvY2tldFxuICBjb25zdCBjb25uZWN0ID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIC8vIFVzZSByZWZzIHRvIGNoZWNrIGN1cnJlbnQgc3RhdGUgaW5zdGVhZCBvZiBzdGF0ZSBkZXBlbmRlbmNpZXNcbiAgICBpZiAoIXdvcmtmbG93SWQgfHwgaXNDb25uZWN0ZWRSZWYuY3VycmVudCB8fCBpc0Nvbm5lY3RpbmdSZWYuY3VycmVudCkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIGlzQ29ubmVjdGluZ1JlZi5jdXJyZW50ID0gdHJ1ZTtcbiAgICBzZXRTdGF0ZShwcmV2ID0+ICh7IC4uLnByZXYsIGlzQ29ubmVjdGluZzogdHJ1ZSwgZXJyb3I6IG51bGwgfSkpO1xuICAgIGlzTWFudWFsRGlzY29ubmVjdFJlZi5jdXJyZW50ID0gZmFsc2U7XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgZXZlbnRTb3VyY2UgPSBuZXcgRXZlbnRTb3VyY2UoYC9hcGkvd29ya2Zsb3cvc3RyZWFtLyR7d29ya2Zsb3dJZH1gKTtcbiAgICAgIGV2ZW50U291cmNlUmVmLmN1cnJlbnQgPSBldmVudFNvdXJjZTtcblxuICAgICAgZXZlbnRTb3VyY2Uub25vcGVuID0gKCkgPT4ge1xuICAgICAgICBjb25zb2xlLmxvZyhgW1dvcmtmbG93IFdlYlNvY2tldF0gQ29ubmVjdGVkIHRvIHdvcmtmbG93ICR7d29ya2Zsb3dJZH1gKTtcbiAgICAgICAgaXNDb25uZWN0ZWRSZWYuY3VycmVudCA9IHRydWU7XG4gICAgICAgIGlzQ29ubmVjdGluZ1JlZi5jdXJyZW50ID0gZmFsc2U7XG4gICAgICAgIHNldFN0YXRlKHByZXYgPT4gKHtcbiAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgIGlzQ29ubmVjdGVkOiB0cnVlLFxuICAgICAgICAgIGlzQ29ubmVjdGluZzogZmFsc2UsXG4gICAgICAgICAgZXJyb3I6IG51bGwsXG4gICAgICAgICAgY29ubmVjdGlvbkNvdW50OiBwcmV2LmNvbm5lY3Rpb25Db3VudCArIDFcbiAgICAgICAgfSkpO1xuICAgICAgICBvbkNvbm5lY3Q/LigpO1xuICAgICAgfTtcblxuICAgICAgZXZlbnRTb3VyY2Uub25tZXNzYWdlID0gKGV2ZW50KSA9PiB7XG4gICAgICAgIHRyeSB7XG4gICAgICAgICAgY29uc3Qgd29ya2Zsb3dFdmVudDogV29ya2Zsb3dXZWJTb2NrZXRFdmVudCA9IEpTT04ucGFyc2UoZXZlbnQuZGF0YSk7XG5cbiAgICAgICAgICBzZXRTdGF0ZShwcmV2ID0+IHtcbiAgICAgICAgICAgIGNvbnN0IG5ld0V2ZW50cyA9IFsuLi5wcmV2LmV2ZW50cywgd29ya2Zsb3dFdmVudF07XG4gICAgICAgICAgICAvLyBLZWVwIG9ubHkgdGhlIGxhc3QgbWF4RXZlbnRzXG4gICAgICAgICAgICBpZiAobmV3RXZlbnRzLmxlbmd0aCA+IG1heEV2ZW50cykge1xuICAgICAgICAgICAgICBuZXdFdmVudHMuc3BsaWNlKDAsIG5ld0V2ZW50cy5sZW5ndGggLSBtYXhFdmVudHMpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAuLi5wcmV2LFxuICAgICAgICAgICAgICBsYXN0RXZlbnQ6IHdvcmtmbG93RXZlbnQsXG4gICAgICAgICAgICAgIGV2ZW50czogbmV3RXZlbnRzXG4gICAgICAgICAgICB9O1xuICAgICAgICAgIH0pO1xuXG4gICAgICAgICAgb25FdmVudD8uKHdvcmtmbG93RXZlbnQpO1xuICAgICAgICAgIGNvbnNvbGUubG9nKGBbV29ya2Zsb3cgV2ViU29ja2V0XSBSZWNlaXZlZCBldmVudDpgLCB3b3JrZmxvd0V2ZW50KTtcbiAgICAgICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICBjb25zb2xlLmVycm9yKCdbV29ya2Zsb3cgV2ViU29ja2V0XSBGYWlsZWQgdG8gcGFyc2UgZXZlbnQ6JywgZXJyb3IpO1xuICAgICAgICB9XG4gICAgICB9O1xuXG4gICAgICBldmVudFNvdXJjZS5vbmVycm9yID0gKGVycm9yKSA9PiB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoYFtXb3JrZmxvdyBXZWJTb2NrZXRdIENvbm5lY3Rpb24gZXJyb3I6YCwgZXJyb3IpO1xuXG4gICAgICAgIGlzQ29ubmVjdGVkUmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICAgICAgaXNDb25uZWN0aW5nUmVmLmN1cnJlbnQgPSBmYWxzZTtcbiAgICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgICAgIC4uLnByZXYsXG4gICAgICAgICAgaXNDb25uZWN0ZWQ6IGZhbHNlLFxuICAgICAgICAgIGlzQ29ubmVjdGluZzogZmFsc2UsXG4gICAgICAgICAgZXJyb3I6ICdDb25uZWN0aW9uIGVycm9yJ1xuICAgICAgICB9KSk7XG5cbiAgICAgICAgb25FcnJvcj8uKCdDb25uZWN0aW9uIGVycm9yJyk7XG5cbiAgICAgICAgLy8gQXV0by1yZWNvbm5lY3QgaWYgbm90IG1hbnVhbGx5IGRpc2Nvbm5lY3RlZFxuICAgICAgICBpZiAoIWlzTWFudWFsRGlzY29ubmVjdFJlZi5jdXJyZW50ICYmIHJlY29ubmVjdEludGVydmFsID4gMCkge1xuICAgICAgICAgIHJlY29ubmVjdFRpbWVvdXRSZWYuY3VycmVudCA9IHNldFRpbWVvdXQoKCkgPT4ge1xuICAgICAgICAgICAgY29uc29sZS5sb2coYFtXb3JrZmxvdyBXZWJTb2NrZXRdIEF0dGVtcHRpbmcgdG8gcmVjb25uZWN0Li4uYCk7XG4gICAgICAgICAgICBjb25uZWN0KCk7XG4gICAgICAgICAgfSwgcmVjb25uZWN0SW50ZXJ2YWwpO1xuICAgICAgICB9XG4gICAgICB9O1xuXG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1tXb3JrZmxvdyBXZWJTb2NrZXRdIEZhaWxlZCB0byBjcmVhdGUgY29ubmVjdGlvbjonLCBlcnJvcik7XG4gICAgICBpc0Nvbm5lY3RpbmdSZWYuY3VycmVudCA9IGZhbHNlO1xuICAgICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICBpc0Nvbm5lY3Rpbmc6IGZhbHNlLFxuICAgICAgICBlcnJvcjogJ0ZhaWxlZCB0byBjcmVhdGUgY29ubmVjdGlvbidcbiAgICAgIH0pKTtcbiAgICAgIG9uRXJyb3I/LignRmFpbGVkIHRvIGNyZWF0ZSBjb25uZWN0aW9uJyk7XG4gICAgfVxuICB9LCBbd29ya2Zsb3dJZCwgbWF4RXZlbnRzLCByZWNvbm5lY3RJbnRlcnZhbCwgb25Db25uZWN0LCBvbkV2ZW50LCBvbkVycm9yXSk7XG5cbiAgLy8gRGlzY29ubmVjdCBmcm9tIFdlYlNvY2tldFxuICBjb25zdCBkaXNjb25uZWN0ID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIGlzTWFudWFsRGlzY29ubmVjdFJlZi5jdXJyZW50ID0gdHJ1ZTtcblxuICAgIGlmIChyZWNvbm5lY3RUaW1lb3V0UmVmLmN1cnJlbnQpIHtcbiAgICAgIGNsZWFyVGltZW91dChyZWNvbm5lY3RUaW1lb3V0UmVmLmN1cnJlbnQpO1xuICAgICAgcmVjb25uZWN0VGltZW91dFJlZi5jdXJyZW50ID0gbnVsbDtcbiAgICB9XG5cbiAgICBpZiAoZXZlbnRTb3VyY2VSZWYuY3VycmVudCkge1xuICAgICAgZXZlbnRTb3VyY2VSZWYuY3VycmVudC5jbG9zZSgpO1xuICAgICAgZXZlbnRTb3VyY2VSZWYuY3VycmVudCA9IG51bGw7XG4gICAgfVxuXG4gICAgc2V0U3RhdGUocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIGlzQ29ubmVjdGVkOiBmYWxzZSxcbiAgICAgIGlzQ29ubmVjdGluZzogZmFsc2UsXG4gICAgICBlcnJvcjogbnVsbFxuICAgIH0pKTtcblxuICAgIG9uRGlzY29ubmVjdD8uKCk7XG4gICAgY29uc29sZS5sb2coYFtXb3JrZmxvdyBXZWJTb2NrZXRdIERpc2Nvbm5lY3RlZCBmcm9tIHdvcmtmbG93ICR7d29ya2Zsb3dJZH1gKTtcbiAgICAvLyBVc2luZyB3b3JrZmxvd0lkIGZyb20gY2xvc3VyZSwgbm90IGFzIGRlcGVuZGVuY3lcbiAgfSwgW29uRGlzY29ubmVjdF0pO1xuXG4gIC8vIFNlbmQgZXZlbnQgdG8gd29ya2Zsb3dcbiAgY29uc3Qgc2VuZEV2ZW50ID0gdXNlQ2FsbGJhY2soYXN5bmMgKFxuICAgIGV2ZW50VHlwZTogc3RyaW5nLFxuICAgIGRhdGE6IGFueSxcbiAgICBleGVjdXRpb25JZD86IHN0cmluZ1xuICApOiBQcm9taXNlPHZvaWQ+ID0+IHtcbiAgICBpZiAoIXdvcmtmbG93SWQpIHtcbiAgICAgIHRocm93IG5ldyBFcnJvcignTm8gd29ya2Zsb3cgSUQgcHJvdmlkZWQnKTtcbiAgICB9XG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgL2FwaS93b3JrZmxvdy9zdHJlYW0vJHt3b3JrZmxvd0lkfWAsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICB9LFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgZXZlbnRUeXBlLFxuICAgICAgICAgIGRhdGEsXG4gICAgICAgICAgZXhlY3V0aW9uSWRcbiAgICAgICAgfSlcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgRmFpbGVkIHRvIHNlbmQgZXZlbnQ6ICR7cmVzcG9uc2Uuc3RhdHVzVGV4dH1gKTtcbiAgICAgIH1cblxuICAgICAgY29uc29sZS5sb2coYFtXb3JrZmxvdyBXZWJTb2NrZXRdIFNlbnQgZXZlbnQgJHtldmVudFR5cGV9IHRvIHdvcmtmbG93ICR7d29ya2Zsb3dJZH1gKTtcbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignW1dvcmtmbG93IFdlYlNvY2tldF0gRmFpbGVkIHRvIHNlbmQgZXZlbnQ6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9LCBbd29ya2Zsb3dJZF0pO1xuXG4gIC8vIENsZWFyIGV2ZW50cyBoaXN0b3J5XG4gIGNvbnN0IGNsZWFyRXZlbnRzID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHNldFN0YXRlKHByZXYgPT4gKHtcbiAgICAgIC4uLnByZXYsXG4gICAgICBldmVudHM6IFtdLFxuICAgICAgbGFzdEV2ZW50OiBudWxsXG4gICAgfSkpO1xuICB9LCBbXSk7XG5cbiAgLy8gUmVjb25uZWN0XG4gIGNvbnN0IHJlY29ubmVjdCA9IHVzZUNhbGxiYWNrKCgpID0+IHtcbiAgICBkaXNjb25uZWN0KCk7XG4gICAgc2V0VGltZW91dChjb25uZWN0LCAxMDApO1xuICB9LCBbZGlzY29ubmVjdCwgY29ubmVjdF0pO1xuXG4gIC8vIEF1dG8tY29ubmVjdCBvbiBtb3VudCBvciB3b3JrZmxvd0lkIGNoYW5nZVxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChhdXRvQ29ubmVjdCAmJiB3b3JrZmxvd0lkKSB7XG4gICAgICAvLyBVc2UgYSB0aW1lb3V0IHRvIGF2b2lkIGltbWVkaWF0ZSBzdGF0ZSBjaGVja3MgdGhhdCBjb3VsZCBjYXVzZSBsb29wc1xuICAgICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICAgIHNldFN0YXRlKHByZXYgPT4ge1xuICAgICAgICAgIGlmICghcHJldi5pc0Nvbm5lY3RlZCAmJiAhcHJldi5pc0Nvbm5lY3RpbmcpIHtcbiAgICAgICAgICAgIGNvbm5lY3QoKTtcbiAgICAgICAgICB9XG4gICAgICAgICAgcmV0dXJuIHByZXY7XG4gICAgICAgIH0pO1xuICAgICAgfSwgMCk7XG5cbiAgICAgIHJldHVybiAoKSA9PiB7XG4gICAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgICAgICBpZiAocmVjb25uZWN0VGltZW91dFJlZi5jdXJyZW50KSB7XG4gICAgICAgICAgY2xlYXJUaW1lb3V0KHJlY29ubmVjdFRpbWVvdXRSZWYuY3VycmVudCk7XG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgfVxuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGlmIChyZWNvbm5lY3RUaW1lb3V0UmVmLmN1cnJlbnQpIHtcbiAgICAgICAgY2xlYXJUaW1lb3V0KHJlY29ubmVjdFRpbWVvdXRSZWYuY3VycmVudCk7XG4gICAgICB9XG4gICAgfTtcbiAgfSwgW3dvcmtmbG93SWQsIGF1dG9Db25uZWN0LCBjb25uZWN0XSk7XG5cbiAgLy8gQ2xlYW51cCBvbiB1bm1vdW50XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGRpc2Nvbm5lY3QoKTtcbiAgICB9O1xuICB9LCBbZGlzY29ubmVjdF0pO1xuXG4gIGNvbnN0IGFjdGlvbnM6IFdvcmtmbG93V2ViU29ja2V0QWN0aW9ucyA9IHtcbiAgICBjb25uZWN0LFxuICAgIGRpc2Nvbm5lY3QsXG4gICAgc2VuZEV2ZW50LFxuICAgIGNsZWFyRXZlbnRzLFxuICAgIHJlY29ubmVjdFxuICB9O1xuXG4gIHJldHVybiBbc3RhdGUsIGFjdGlvbnNdO1xufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZVN0YXRlIiwidXNlQ2FsbGJhY2siLCJ1c2VXb3JrZmxvd1dlYlNvY2tldCIsIndvcmtmbG93SWQiLCJvcHRpb25zIiwiYXV0b0Nvbm5lY3QiLCJtYXhFdmVudHMiLCJyZWNvbm5lY3RJbnRlcnZhbCIsIm9uRXZlbnQiLCJvbkNvbm5lY3QiLCJvbkRpc2Nvbm5lY3QiLCJvbkVycm9yIiwic3RhdGUiLCJzZXRTdGF0ZSIsImlzQ29ubmVjdGVkIiwiaXNDb25uZWN0aW5nIiwiZXJyb3IiLCJsYXN0RXZlbnQiLCJldmVudHMiLCJjb25uZWN0aW9uQ291bnQiLCJldmVudFNvdXJjZVJlZiIsInJlY29ubmVjdFRpbWVvdXRSZWYiLCJpc01hbnVhbERpc2Nvbm5lY3RSZWYiLCJpc0Nvbm5lY3RlZFJlZiIsImlzQ29ubmVjdGluZ1JlZiIsImNvbm5lY3QiLCJjdXJyZW50IiwicHJldiIsImV2ZW50U291cmNlIiwiRXZlbnRTb3VyY2UiLCJvbm9wZW4iLCJjb25zb2xlIiwibG9nIiwib25tZXNzYWdlIiwiZXZlbnQiLCJ3b3JrZmxvd0V2ZW50IiwiSlNPTiIsInBhcnNlIiwiZGF0YSIsIm5ld0V2ZW50cyIsImxlbmd0aCIsInNwbGljZSIsIm9uZXJyb3IiLCJzZXRUaW1lb3V0IiwiZGlzY29ubmVjdCIsImNsZWFyVGltZW91dCIsImNsb3NlIiwic2VuZEV2ZW50IiwiZXZlbnRUeXBlIiwiZXhlY3V0aW9uSWQiLCJFcnJvciIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsInN0cmluZ2lmeSIsIm9rIiwic3RhdHVzVGV4dCIsImNsZWFyRXZlbnRzIiwicmVjb25uZWN0IiwidGltZW91dElkIiwiYWN0aW9ucyJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useWorkflowWebSocket.ts\n"));

/***/ })

});