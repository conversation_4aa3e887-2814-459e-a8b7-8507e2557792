"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useWorkflowWebSocket.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useWorkflowWebSocket.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWorkflowWebSocket: () => (/* binding */ useWorkflowWebSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * React Hook for WebSocket workflow updates\n * Provides real-time updates for Manual Build workflows\n */ \nfunction useWorkflowWebSocket(workflowId) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { autoConnect = true, maxEvents = 100, reconnectInterval = 5000, onEvent, onConnect, onDisconnect, onError } = options;\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        isConnected: false,\n        isConnecting: false,\n        error: null,\n        lastEvent: null,\n        events: [],\n        connectionCount: 0\n    });\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const isManualDisconnectRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isConnectedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isConnectingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Connect to WebSocket\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[connect]\": ()=>{\n            // Use refs to check current state instead of state dependencies\n            if (!workflowId || isConnectedRef.current || isConnectingRef.current) {\n                return;\n            }\n            isConnectingRef.current = true;\n            setState({\n                \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>({\n                        ...prev,\n                        isConnecting: true,\n                        error: null\n                    })\n            }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n            isManualDisconnectRef.current = false;\n            try {\n                const eventSource = new EventSource(\"/api/workflow/stream/\".concat(workflowId));\n                eventSourceRef.current = eventSource;\n                eventSource.onopen = ({\n                    \"useWorkflowWebSocket.useCallback[connect]\": ()=>{\n                        console.log(\"[Workflow WebSocket] Connected to workflow \".concat(workflowId));\n                        isConnectedRef.current = true;\n                        isConnectingRef.current = false;\n                        setState({\n                            \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>({\n                                    ...prev,\n                                    isConnected: true,\n                                    isConnecting: false,\n                                    error: null,\n                                    connectionCount: prev.connectionCount + 1\n                                })\n                        }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n                        onConnect === null || onConnect === void 0 ? void 0 : onConnect();\n                    }\n                })[\"useWorkflowWebSocket.useCallback[connect]\"];\n                eventSource.onmessage = ({\n                    \"useWorkflowWebSocket.useCallback[connect]\": (event)=>{\n                        try {\n                            const workflowEvent = JSON.parse(event.data);\n                            setState({\n                                \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>{\n                                    const newEvents = [\n                                        ...prev.events,\n                                        workflowEvent\n                                    ];\n                                    // Keep only the last maxEvents\n                                    if (newEvents.length > maxEvents) {\n                                        newEvents.splice(0, newEvents.length - maxEvents);\n                                    }\n                                    return {\n                                        ...prev,\n                                        lastEvent: workflowEvent,\n                                        events: newEvents\n                                    };\n                                }\n                            }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n                            onEvent === null || onEvent === void 0 ? void 0 : onEvent(workflowEvent);\n                            console.log(\"[Workflow WebSocket] Received event:\", workflowEvent);\n                        } catch (error) {\n                            console.error('[Workflow WebSocket] Failed to parse event:', error);\n                        }\n                    }\n                })[\"useWorkflowWebSocket.useCallback[connect]\"];\n                eventSource.onerror = ({\n                    \"useWorkflowWebSocket.useCallback[connect]\": (error)=>{\n                        console.error(\"[Workflow WebSocket] Connection error:\", error);\n                        isConnectedRef.current = false;\n                        isConnectingRef.current = false;\n                        setState({\n                            \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>({\n                                    ...prev,\n                                    isConnected: false,\n                                    isConnecting: false,\n                                    error: 'Connection error'\n                                })\n                        }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n                        onError === null || onError === void 0 ? void 0 : onError('Connection error');\n                        // Auto-reconnect if not manually disconnected\n                        if (!isManualDisconnectRef.current && reconnectInterval > 0) {\n                            reconnectTimeoutRef.current = setTimeout({\n                                \"useWorkflowWebSocket.useCallback[connect]\": ()=>{\n                                    console.log(\"[Workflow WebSocket] Attempting to reconnect...\");\n                                    connect();\n                                }\n                            }[\"useWorkflowWebSocket.useCallback[connect]\"], reconnectInterval);\n                        }\n                    }\n                })[\"useWorkflowWebSocket.useCallback[connect]\"];\n            } catch (error) {\n                console.error('[Workflow WebSocket] Failed to create connection:', error);\n                isConnectingRef.current = false;\n                setState({\n                    \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>({\n                            ...prev,\n                            isConnecting: false,\n                            error: 'Failed to create connection'\n                        })\n                }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n                onError === null || onError === void 0 ? void 0 : onError('Failed to create connection');\n            }\n        }\n    }[\"useWorkflowWebSocket.useCallback[connect]\"], [\n        workflowId,\n        maxEvents,\n        reconnectInterval,\n        onConnect,\n        onEvent,\n        onError\n    ]);\n    // Disconnect from WebSocket\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[disconnect]\": ()=>{\n            isManualDisconnectRef.current = true;\n            isConnectedRef.current = false;\n            isConnectingRef.current = false;\n            if (reconnectTimeoutRef.current) {\n                clearTimeout(reconnectTimeoutRef.current);\n                reconnectTimeoutRef.current = null;\n            }\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n                eventSourceRef.current = null;\n            }\n            setState({\n                \"useWorkflowWebSocket.useCallback[disconnect]\": (prev)=>({\n                        ...prev,\n                        isConnected: false,\n                        isConnecting: false,\n                        error: null\n                    })\n            }[\"useWorkflowWebSocket.useCallback[disconnect]\"]);\n            onDisconnect === null || onDisconnect === void 0 ? void 0 : onDisconnect();\n            console.log(\"[Workflow WebSocket] Disconnected from workflow \".concat(workflowId));\n        }\n    }[\"useWorkflowWebSocket.useCallback[disconnect]\"], [\n        onDisconnect,\n        workflowId\n    ]);\n    // Send event to workflow\n    const sendEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[sendEvent]\": async (eventType, data, executionId)=>{\n            if (!workflowId) {\n                throw new Error('No workflow ID provided');\n            }\n            try {\n                const response = await fetch(\"/api/workflow/stream/\".concat(workflowId), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        eventType,\n                        data,\n                        executionId\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to send event: \".concat(response.statusText));\n                }\n                console.log(\"[Workflow WebSocket] Sent event \".concat(eventType, \" to workflow \").concat(workflowId));\n            } catch (error) {\n                console.error('[Workflow WebSocket] Failed to send event:', error);\n                throw error;\n            }\n        }\n    }[\"useWorkflowWebSocket.useCallback[sendEvent]\"], [\n        workflowId\n    ]);\n    // Clear events history\n    const clearEvents = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[clearEvents]\": ()=>{\n            setState({\n                \"useWorkflowWebSocket.useCallback[clearEvents]\": (prev)=>({\n                        ...prev,\n                        events: [],\n                        lastEvent: null\n                    })\n            }[\"useWorkflowWebSocket.useCallback[clearEvents]\"]);\n        }\n    }[\"useWorkflowWebSocket.useCallback[clearEvents]\"], []);\n    // Reconnect\n    const reconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[reconnect]\": ()=>{\n            disconnect();\n            setTimeout(connect, 100);\n        }\n    }[\"useWorkflowWebSocket.useCallback[reconnect]\"], [\n        disconnect,\n        connect\n    ]);\n    // Auto-connect on mount or workflowId change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWorkflowWebSocket.useEffect\": ()=>{\n            if (autoConnect && workflowId && !isConnectedRef.current && !isConnectingRef.current) {\n                connect();\n            }\n            return ({\n                \"useWorkflowWebSocket.useEffect\": ()=>{\n                    if (reconnectTimeoutRef.current) {\n                        clearTimeout(reconnectTimeoutRef.current);\n                    }\n                }\n            })[\"useWorkflowWebSocket.useEffect\"];\n        }\n    }[\"useWorkflowWebSocket.useEffect\"], [\n        workflowId,\n        autoConnect,\n        connect\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWorkflowWebSocket.useEffect\": ()=>{\n            return ({\n                \"useWorkflowWebSocket.useEffect\": ()=>{\n                    // Cleanup without calling disconnect to avoid dependency issues\n                    isManualDisconnectRef.current = true;\n                    isConnectedRef.current = false;\n                    isConnectingRef.current = false;\n                    if (reconnectTimeoutRef.current) {\n                        clearTimeout(reconnectTimeoutRef.current);\n                        reconnectTimeoutRef.current = null;\n                    }\n                    if (eventSourceRef.current) {\n                        eventSourceRef.current.close();\n                        eventSourceRef.current = null;\n                    }\n                }\n            })[\"useWorkflowWebSocket.useEffect\"];\n        }\n    }[\"useWorkflowWebSocket.useEffect\"], []);\n    const actions = {\n        connect,\n        disconnect,\n        sendEvent,\n        clearEvents,\n        reconnect\n    };\n    return [\n        state,\n        actions\n    ];\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useWorkflowWebSocket.ts\n"));

/***/ })

});