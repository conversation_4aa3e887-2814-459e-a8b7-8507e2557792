"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/NodePalette.tsx":
/*!*****************************************************!*\
  !*** ./src/components/manual-build/NodePalette.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ NodePalette)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CloudIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/GlobeAltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronDownIcon,ChevronRightIcon,CircleStackIcon,ClipboardDocumentListIcon,CloudIcon,CpuChipIcon,DocumentTextIcon,EyeIcon,GlobeAltIcon,UserGroupIcon,UserIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronRightIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst nodeCategories = {\n    core: {\n        label: 'Core Nodes',\n        description: 'Essential workflow components',\n        nodes: [\n            {\n                type: 'userRequest',\n                label: 'User Request',\n                description: 'Starting point for user input',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n                category: 'core',\n                isAvailable: true,\n                defaultData: {\n                    label: 'User Request',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'classifier',\n                label: 'Classifier',\n                description: 'Analyzes and categorizes requests',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                category: 'core',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Classifier',\n                    config: {},\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'output',\n                label: 'Output',\n                description: 'Final response to user',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n                category: 'core',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Output',\n                    config: {},\n                    isConfigured: true\n                }\n            }\n        ]\n    },\n    ai: {\n        label: 'AI Providers',\n        description: 'AI model integrations',\n        nodes: [\n            {\n                type: 'provider',\n                label: 'AI Provider',\n                description: 'Connect to AI models (OpenAI, Claude, etc.)',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                category: 'ai',\n                isAvailable: true,\n                defaultData: {\n                    label: 'AI Provider',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 1.0,\n                            maxTokens: undefined,\n                            topP: undefined,\n                            frequencyPenalty: undefined,\n                            presencePenalty: undefined\n                        }\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'vision',\n                label: 'Vision AI',\n                description: 'Multimodal AI for image analysis and vision tasks',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                category: 'ai',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Vision AI',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 1.0,\n                            maxTokens: undefined,\n                            topP: undefined,\n                            frequencyPenalty: undefined,\n                            presencePenalty: undefined\n                        }\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'roleAgent',\n                label: 'Role Agent',\n                description: 'Role plugin for AI providers (connect to role input)',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                category: 'ai',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Role Agent',\n                    config: {\n                        roleId: '',\n                        roleName: '',\n                        roleType: 'predefined',\n                        customPrompt: '',\n                        memoryEnabled: false\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'centralRouter',\n                label: 'Central Router',\n                description: 'Smart routing hub for multiple AI providers and vision models',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n                category: 'ai',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Central Router',\n                    config: {\n                        routingStrategy: 'smart',\n                        fallbackProvider: '',\n                        maxRetries: 3,\n                        timeout: 30000,\n                        enableCaching: true,\n                        debugMode: false\n                    },\n                    isConfigured: true\n                }\n            },\n            {\n                type: 'planner',\n                label: 'Planner',\n                description: 'AI model that creates browsing strategies and todo lists',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                category: 'ai',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Planner',\n                    config: {\n                        providerId: '',\n                        modelId: '',\n                        apiKey: '',\n                        parameters: {\n                            temperature: 0.7,\n                            maxTokens: 1000\n                        },\n                        maxSubtasks: 10\n                    },\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    tools: {\n        label: 'Tools & Integrations',\n        description: 'External service integrations',\n        nodes: [\n            {\n                type: 'tool',\n                label: 'Tools',\n                description: 'External tool integrations (Google Drive, Zapier, etc.)',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                category: 'tools',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Tools',\n                    config: {\n                        toolType: '',\n                        toolConfig: {},\n                        timeout: 30,\n                        connectionStatus: 'disconnected',\n                        isAuthenticated: false\n                    },\n                    isConfigured: false\n                }\n            },\n            {\n                type: 'memory',\n                label: 'Memory',\n                description: 'Store and retrieve data across workflow executions',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                category: 'advanced',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Memory',\n                    config: {\n                        memoryName: '',\n                        maxSize: 10240,\n                        encryption: true,\n                        description: ''\n                    },\n                    isConfigured: false\n                }\n            }\n        ]\n    },\n    browsing: {\n        label: 'Web Browsing',\n        description: 'Intelligent web browsing and automation',\n        nodes: [\n            {\n                type: 'browsing',\n                label: 'Browsing Agent',\n                description: 'Intelligent web browsing agent with multi-step automation',\n                icon: _barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                category: 'advanced',\n                isAvailable: true,\n                defaultData: {\n                    label: 'Browsing Agent',\n                    config: {\n                        maxSites: 5,\n                        timeout: 30,\n                        enableScreenshots: true,\n                        enableFormFilling: true,\n                        enableCaptchaSolving: false,\n                        searchEngines: [\n                            'google'\n                        ],\n                        maxDepth: 2,\n                        respectRobots: true,\n                        enableJavaScript: true\n                    },\n                    isConfigured: true\n                }\n            }\n        ]\n    }\n};\nfunction NodeItem(param) {\n    let { node, onAddNode } = param;\n    const Icon = node.icon;\n    const handleDragStart = (event)=>{\n        event.dataTransfer.setData('application/reactflow', node.type);\n        event.dataTransfer.effectAllowed = 'move';\n    };\n    const handleClick = ()=>{\n        // Add node at center of canvas\n        onAddNode(node.type);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        draggable: true,\n        onDragStart: handleDragStart,\n        onClick: handleClick,\n        className: \"p-3 rounded-lg border cursor-pointer transition-all duration-200 \".concat(node.isAvailable ? 'bg-gray-800/50 border-gray-700/50 hover:border-[#ff6b35]/50 hover:bg-gray-700/50' : 'bg-gray-900/50 border-gray-800/50 opacity-50 cursor-not-allowed'),\n        title: node.description,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-2 rounded-lg \".concat(node.isAvailable ? 'bg-[#ff6b35]/20 text-[#ff6b35]' : 'bg-gray-700/50 text-gray-500'),\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                        className: \"w-4 h-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                    lineNumber: 282,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"font-medium text-sm \".concat(node.isAvailable ? 'text-white' : 'text-gray-500'),\n                            children: node.label\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                            lineNumber: 288,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-xs text-gray-400 truncate\",\n                            children: node.description\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                    lineNumber: 287,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n            lineNumber: 281,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 270,\n        columnNumber: 5\n    }, this);\n}\n_c = NodeItem;\nfunction CategorySection(param) {\n    let { category, data, isExpanded, onToggle, onAddNode } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"mb-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                onClick: onToggle,\n                className: \"w-full flex items-center justify-between p-3 bg-gray-800/30 hover:bg-gray-800/50 rounded-lg transition-colors\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            isExpanded ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronDownIcon_ChevronRightIcon_CircleStackIcon_ClipboardDocumentListIcon_CloudIcon_CpuChipIcon_DocumentTextIcon_EyeIcon_GlobeAltIcon_UserGroupIcon_UserIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-4 h-4 text-gray-400\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-medium text-white\",\n                                children: data.label\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"text-xs text-gray-400\",\n                        children: data.nodes.length\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 329,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 317,\n                columnNumber: 7\n            }, this),\n            isExpanded && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-2 space-y-2\",\n                children: data.nodes.map((node)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(NodeItem, {\n                        node: node,\n                        onAddNode: onAddNode\n                    }, node.type, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 335,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 333,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 316,\n        columnNumber: 5\n    }, this);\n}\n_c1 = CategorySection;\nfunction NodePalette(param) {\n    let { onAddNode } = param;\n    _s();\n    const [expandedCategories, setExpandedCategories] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set([\n        'core',\n        'ai'\n    ]) // Expand core and AI categories by default\n    );\n    const toggleCategory = (category)=>{\n        const newExpanded = new Set(expandedCategories);\n        if (newExpanded.has(category)) {\n            newExpanded.delete(category);\n        } else {\n            newExpanded.add(category);\n        }\n        setExpandedCategories(newExpanded);\n    };\n    const handleAddNode = (nodeType)=>{\n        // Add node at a default position (center of canvas)\n        onAddNode(nodeType, {\n            x: 400,\n            y: 200\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-80 bg-gray-900/80 backdrop-blur-sm border-r border-gray-700/50 p-4 overflow-y-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-lg font-semibold text-white mb-2\",\n                        children: \"Node Palette\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-gray-400\",\n                        children: \"Drag nodes to the canvas or click to add at center\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 371,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 369,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-1\",\n                children: Object.entries(nodeCategories).map((param)=>{\n                    let [category, data] = param;\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CategorySection, {\n                        category: category,\n                        data: data,\n                        isExpanded: expandedCategories.has(category),\n                        onToggle: ()=>toggleCategory(category),\n                        onAddNode: handleAddNode\n                    }, category, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 378,\n                        columnNumber: 11\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 376,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 p-3 bg-blue-900/20 border border-blue-700/30 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-blue-300 font-medium mb-1\",\n                        children: \"\\uD83D\\uDCA1 Pro Tip\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-blue-200\",\n                        children: \"Connect nodes by dragging from output handles to input handles. Every workflow must end with an Output node.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                        lineNumber: 391,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n                lineNumber: 389,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\NodePalette.tsx\",\n        lineNumber: 368,\n        columnNumber: 5\n    }, this);\n}\n_s(NodePalette, \"kKRKUKeIglQBeO0mlMXPYOz8OQo=\");\n_c2 = NodePalette;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"NodeItem\");\n$RefreshReg$(_c1, \"CategorySection\");\n$RefreshReg$(_c2, \"NodePalette\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/NodePalette.tsx\n"));

/***/ })

});