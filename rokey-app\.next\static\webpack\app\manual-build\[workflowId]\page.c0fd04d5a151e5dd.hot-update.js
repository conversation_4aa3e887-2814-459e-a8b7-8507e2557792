"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useWorkflowWebSocket.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useWorkflowWebSocket.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWorkflowWebSocket: () => (/* binding */ useWorkflowWebSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * React Hook for WebSocket workflow updates\n * Provides real-time updates for Manual Build workflows\n */ \nfunction useWorkflowWebSocket(workflowId) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { autoConnect = true, maxEvents = 100, reconnectInterval = 5000, onEvent, onConnect, onDisconnect, onError } = options;\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        isConnected: false,\n        isConnecting: false,\n        error: null,\n        lastEvent: null,\n        events: [],\n        connectionCount: 0\n    });\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const isManualDisconnectRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Connect to WebSocket\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[connect]\": ()=>{\n            // Use refs to check current state instead of state dependencies\n            if (!workflowId) {\n                return;\n            }\n            setState({\n                \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>{\n                    // Check state inside setState to avoid stale closures\n                    if (prev.isConnected || prev.isConnecting) {\n                        return prev;\n                    }\n                    return {\n                        ...prev,\n                        isConnecting: true,\n                        error: null\n                    };\n                }\n            }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n            isManualDisconnectRef.current = false;\n            try {\n                const eventSource = new EventSource(\"/api/workflow/stream/\".concat(workflowId));\n                eventSourceRef.current = eventSource;\n                eventSource.onopen = ({\n                    \"useWorkflowWebSocket.useCallback[connect]\": ()=>{\n                        console.log(\"[Workflow WebSocket] Connected to workflow \".concat(workflowId));\n                        setState({\n                            \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>({\n                                    ...prev,\n                                    isConnected: true,\n                                    isConnecting: false,\n                                    error: null,\n                                    connectionCount: prev.connectionCount + 1\n                                })\n                        }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n                        onConnect === null || onConnect === void 0 ? void 0 : onConnect();\n                    }\n                })[\"useWorkflowWebSocket.useCallback[connect]\"];\n                eventSource.onmessage = ({\n                    \"useWorkflowWebSocket.useCallback[connect]\": (event)=>{\n                        try {\n                            const workflowEvent = JSON.parse(event.data);\n                            setState({\n                                \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>{\n                                    const newEvents = [\n                                        ...prev.events,\n                                        workflowEvent\n                                    ];\n                                    // Keep only the last maxEvents\n                                    if (newEvents.length > maxEvents) {\n                                        newEvents.splice(0, newEvents.length - maxEvents);\n                                    }\n                                    return {\n                                        ...prev,\n                                        lastEvent: workflowEvent,\n                                        events: newEvents\n                                    };\n                                }\n                            }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n                            onEvent === null || onEvent === void 0 ? void 0 : onEvent(workflowEvent);\n                            console.log(\"[Workflow WebSocket] Received event:\", workflowEvent);\n                        } catch (error) {\n                            console.error('[Workflow WebSocket] Failed to parse event:', error);\n                        }\n                    }\n                })[\"useWorkflowWebSocket.useCallback[connect]\"];\n                eventSource.onerror = ({\n                    \"useWorkflowWebSocket.useCallback[connect]\": (error)=>{\n                        console.error(\"[Workflow WebSocket] Connection error:\", error);\n                        setState({\n                            \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>({\n                                    ...prev,\n                                    isConnected: false,\n                                    isConnecting: false,\n                                    error: 'Connection error'\n                                })\n                        }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n                        onError === null || onError === void 0 ? void 0 : onError('Connection error');\n                        // Auto-reconnect if not manually disconnected\n                        if (!isManualDisconnectRef.current && reconnectInterval > 0) {\n                            reconnectTimeoutRef.current = setTimeout({\n                                \"useWorkflowWebSocket.useCallback[connect]\": ()=>{\n                                    console.log(\"[Workflow WebSocket] Attempting to reconnect...\");\n                                    connect();\n                                }\n                            }[\"useWorkflowWebSocket.useCallback[connect]\"], reconnectInterval);\n                        }\n                    }\n                })[\"useWorkflowWebSocket.useCallback[connect]\"];\n            } catch (error) {\n                console.error('[Workflow WebSocket] Failed to create connection:', error);\n                setState({\n                    \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>({\n                            ...prev,\n                            isConnecting: false,\n                            error: 'Failed to create connection'\n                        })\n                }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n                onError === null || onError === void 0 ? void 0 : onError('Failed to create connection');\n            }\n        }\n    }[\"useWorkflowWebSocket.useCallback[connect]\"], [\n        workflowId,\n        maxEvents,\n        reconnectInterval,\n        onConnect,\n        onEvent,\n        onError\n    ]);\n    // Disconnect from WebSocket\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[disconnect]\": ()=>{\n            isManualDisconnectRef.current = true;\n            if (reconnectTimeoutRef.current) {\n                clearTimeout(reconnectTimeoutRef.current);\n                reconnectTimeoutRef.current = null;\n            }\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n                eventSourceRef.current = null;\n            }\n            setState({\n                \"useWorkflowWebSocket.useCallback[disconnect]\": (prev)=>({\n                        ...prev,\n                        isConnected: false,\n                        isConnecting: false,\n                        error: null\n                    })\n            }[\"useWorkflowWebSocket.useCallback[disconnect]\"]);\n            onDisconnect === null || onDisconnect === void 0 ? void 0 : onDisconnect();\n            console.log(\"[Workflow WebSocket] Disconnected from workflow \".concat(workflowId));\n        // Using workflowId from closure, not as dependency\n        }\n    }[\"useWorkflowWebSocket.useCallback[disconnect]\"], [\n        onDisconnect\n    ]);\n    // Send event to workflow\n    const sendEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[sendEvent]\": async (eventType, data, executionId)=>{\n            if (!workflowId) {\n                throw new Error('No workflow ID provided');\n            }\n            try {\n                const response = await fetch(\"/api/workflow/stream/\".concat(workflowId), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        eventType,\n                        data,\n                        executionId\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to send event: \".concat(response.statusText));\n                }\n                console.log(\"[Workflow WebSocket] Sent event \".concat(eventType, \" to workflow \").concat(workflowId));\n            } catch (error) {\n                console.error('[Workflow WebSocket] Failed to send event:', error);\n                throw error;\n            }\n        }\n    }[\"useWorkflowWebSocket.useCallback[sendEvent]\"], [\n        workflowId\n    ]);\n    // Clear events history\n    const clearEvents = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[clearEvents]\": ()=>{\n            setState({\n                \"useWorkflowWebSocket.useCallback[clearEvents]\": (prev)=>({\n                        ...prev,\n                        events: [],\n                        lastEvent: null\n                    })\n            }[\"useWorkflowWebSocket.useCallback[clearEvents]\"]);\n        }\n    }[\"useWorkflowWebSocket.useCallback[clearEvents]\"], []);\n    // Reconnect\n    const reconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[reconnect]\": ()=>{\n            disconnect();\n            setTimeout(connect, 100);\n        }\n    }[\"useWorkflowWebSocket.useCallback[reconnect]\"], [\n        disconnect,\n        connect\n    ]);\n    // Auto-connect on mount or workflowId change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWorkflowWebSocket.useEffect\": ()=>{\n            if (autoConnect && workflowId && !state.isConnected && !state.isConnecting) {\n                connect();\n            }\n            return ({\n                \"useWorkflowWebSocket.useEffect\": ()=>{\n                    if (reconnectTimeoutRef.current) {\n                        clearTimeout(reconnectTimeoutRef.current);\n                    }\n                }\n            })[\"useWorkflowWebSocket.useEffect\"];\n        }\n    }[\"useWorkflowWebSocket.useEffect\"], [\n        workflowId,\n        autoConnect,\n        connect,\n        state.isConnected,\n        state.isConnecting\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWorkflowWebSocket.useEffect\": ()=>{\n            return ({\n                \"useWorkflowWebSocket.useEffect\": ()=>{\n                    disconnect();\n                }\n            })[\"useWorkflowWebSocket.useEffect\"];\n        }\n    }[\"useWorkflowWebSocket.useEffect\"], [\n        disconnect\n    ]);\n    const actions = {\n        connect,\n        disconnect,\n        sendEvent,\n        clearEvents,\n        reconnect\n    };\n    return [\n        state,\n        actions\n    ];\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useWorkflowWebSocket.ts\n"));

/***/ })

});