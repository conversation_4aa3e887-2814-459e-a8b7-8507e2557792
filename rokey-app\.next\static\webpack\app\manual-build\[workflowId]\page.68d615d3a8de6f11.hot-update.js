"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/hooks/useWorkflowWebSocket.ts":
/*!*******************************************!*\
  !*** ./src/hooks/useWorkflowWebSocket.ts ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWorkflowWebSocket: () => (/* binding */ useWorkflowWebSocket)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * React Hook for WebSocket workflow updates\n * Provides real-time updates for Manual Build workflows\n */ \nfunction useWorkflowWebSocket(workflowId) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { autoConnect = true, maxEvents = 100, reconnectInterval = 5000, onEvent, onConnect, onDisconnect, onError } = options;\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        isConnected: false,\n        isConnecting: false,\n        error: null,\n        lastEvent: null,\n        events: [],\n        connectionCount: 0\n    });\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const isManualDisconnectRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isConnectedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isConnectingRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    // Connect to WebSocket\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[connect]\": ()=>{\n            // Use refs to check current state instead of state dependencies\n            if (!workflowId) {\n                return;\n            }\n            setState({\n                \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>{\n                    // Check state inside setState to avoid stale closures\n                    if (prev.isConnected || prev.isConnecting) {\n                        return prev;\n                    }\n                    return {\n                        ...prev,\n                        isConnecting: true,\n                        error: null\n                    };\n                }\n            }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n            isManualDisconnectRef.current = false;\n            try {\n                const eventSource = new EventSource(\"/api/workflow/stream/\".concat(workflowId));\n                eventSourceRef.current = eventSource;\n                eventSource.onopen = ({\n                    \"useWorkflowWebSocket.useCallback[connect]\": ()=>{\n                        console.log(\"[Workflow WebSocket] Connected to workflow \".concat(workflowId));\n                        setState({\n                            \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>({\n                                    ...prev,\n                                    isConnected: true,\n                                    isConnecting: false,\n                                    error: null,\n                                    connectionCount: prev.connectionCount + 1\n                                })\n                        }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n                        onConnect === null || onConnect === void 0 ? void 0 : onConnect();\n                    }\n                })[\"useWorkflowWebSocket.useCallback[connect]\"];\n                eventSource.onmessage = ({\n                    \"useWorkflowWebSocket.useCallback[connect]\": (event)=>{\n                        try {\n                            const workflowEvent = JSON.parse(event.data);\n                            setState({\n                                \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>{\n                                    const newEvents = [\n                                        ...prev.events,\n                                        workflowEvent\n                                    ];\n                                    // Keep only the last maxEvents\n                                    if (newEvents.length > maxEvents) {\n                                        newEvents.splice(0, newEvents.length - maxEvents);\n                                    }\n                                    return {\n                                        ...prev,\n                                        lastEvent: workflowEvent,\n                                        events: newEvents\n                                    };\n                                }\n                            }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n                            onEvent === null || onEvent === void 0 ? void 0 : onEvent(workflowEvent);\n                            console.log(\"[Workflow WebSocket] Received event:\", workflowEvent);\n                        } catch (error) {\n                            console.error('[Workflow WebSocket] Failed to parse event:', error);\n                        }\n                    }\n                })[\"useWorkflowWebSocket.useCallback[connect]\"];\n                eventSource.onerror = ({\n                    \"useWorkflowWebSocket.useCallback[connect]\": (error)=>{\n                        console.error(\"[Workflow WebSocket] Connection error:\", error);\n                        setState({\n                            \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>({\n                                    ...prev,\n                                    isConnected: false,\n                                    isConnecting: false,\n                                    error: 'Connection error'\n                                })\n                        }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n                        onError === null || onError === void 0 ? void 0 : onError('Connection error');\n                        // Auto-reconnect if not manually disconnected\n                        if (!isManualDisconnectRef.current && reconnectInterval > 0) {\n                            reconnectTimeoutRef.current = setTimeout({\n                                \"useWorkflowWebSocket.useCallback[connect]\": ()=>{\n                                    console.log(\"[Workflow WebSocket] Attempting to reconnect...\");\n                                    connect();\n                                }\n                            }[\"useWorkflowWebSocket.useCallback[connect]\"], reconnectInterval);\n                        }\n                    }\n                })[\"useWorkflowWebSocket.useCallback[connect]\"];\n            } catch (error) {\n                console.error('[Workflow WebSocket] Failed to create connection:', error);\n                setState({\n                    \"useWorkflowWebSocket.useCallback[connect]\": (prev)=>({\n                            ...prev,\n                            isConnecting: false,\n                            error: 'Failed to create connection'\n                        })\n                }[\"useWorkflowWebSocket.useCallback[connect]\"]);\n                onError === null || onError === void 0 ? void 0 : onError('Failed to create connection');\n            }\n        }\n    }[\"useWorkflowWebSocket.useCallback[connect]\"], [\n        workflowId,\n        maxEvents,\n        reconnectInterval,\n        onConnect,\n        onEvent,\n        onError\n    ]);\n    // Disconnect from WebSocket\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[disconnect]\": ()=>{\n            isManualDisconnectRef.current = true;\n            if (reconnectTimeoutRef.current) {\n                clearTimeout(reconnectTimeoutRef.current);\n                reconnectTimeoutRef.current = null;\n            }\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n                eventSourceRef.current = null;\n            }\n            setState({\n                \"useWorkflowWebSocket.useCallback[disconnect]\": (prev)=>({\n                        ...prev,\n                        isConnected: false,\n                        isConnecting: false,\n                        error: null\n                    })\n            }[\"useWorkflowWebSocket.useCallback[disconnect]\"]);\n            onDisconnect === null || onDisconnect === void 0 ? void 0 : onDisconnect();\n            console.log(\"[Workflow WebSocket] Disconnected from workflow \".concat(workflowId));\n        // Using workflowId from closure, not as dependency\n        }\n    }[\"useWorkflowWebSocket.useCallback[disconnect]\"], [\n        onDisconnect\n    ]);\n    // Send event to workflow\n    const sendEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[sendEvent]\": async (eventType, data, executionId)=>{\n            if (!workflowId) {\n                throw new Error('No workflow ID provided');\n            }\n            try {\n                const response = await fetch(\"/api/workflow/stream/\".concat(workflowId), {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        eventType,\n                        data,\n                        executionId\n                    })\n                });\n                if (!response.ok) {\n                    throw new Error(\"Failed to send event: \".concat(response.statusText));\n                }\n                console.log(\"[Workflow WebSocket] Sent event \".concat(eventType, \" to workflow \").concat(workflowId));\n            } catch (error) {\n                console.error('[Workflow WebSocket] Failed to send event:', error);\n                throw error;\n            }\n        }\n    }[\"useWorkflowWebSocket.useCallback[sendEvent]\"], [\n        workflowId\n    ]);\n    // Clear events history\n    const clearEvents = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[clearEvents]\": ()=>{\n            setState({\n                \"useWorkflowWebSocket.useCallback[clearEvents]\": (prev)=>({\n                        ...prev,\n                        events: [],\n                        lastEvent: null\n                    })\n            }[\"useWorkflowWebSocket.useCallback[clearEvents]\"]);\n        }\n    }[\"useWorkflowWebSocket.useCallback[clearEvents]\"], []);\n    // Reconnect\n    const reconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useWorkflowWebSocket.useCallback[reconnect]\": ()=>{\n            disconnect();\n            setTimeout(connect, 100);\n        }\n    }[\"useWorkflowWebSocket.useCallback[reconnect]\"], [\n        disconnect,\n        connect\n    ]);\n    // Auto-connect on mount or workflowId change\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWorkflowWebSocket.useEffect\": ()=>{\n            if (autoConnect && workflowId) {\n                // Use a timeout to avoid immediate state checks that could cause loops\n                const timeoutId = setTimeout({\n                    \"useWorkflowWebSocket.useEffect.timeoutId\": ()=>{\n                        setState({\n                            \"useWorkflowWebSocket.useEffect.timeoutId\": (prev)=>{\n                                if (!prev.isConnected && !prev.isConnecting) {\n                                    connect();\n                                }\n                                return prev;\n                            }\n                        }[\"useWorkflowWebSocket.useEffect.timeoutId\"]);\n                    }\n                }[\"useWorkflowWebSocket.useEffect.timeoutId\"], 0);\n                return ({\n                    \"useWorkflowWebSocket.useEffect\": ()=>{\n                        clearTimeout(timeoutId);\n                        if (reconnectTimeoutRef.current) {\n                            clearTimeout(reconnectTimeoutRef.current);\n                        }\n                    }\n                })[\"useWorkflowWebSocket.useEffect\"];\n            }\n            return ({\n                \"useWorkflowWebSocket.useEffect\": ()=>{\n                    if (reconnectTimeoutRef.current) {\n                        clearTimeout(reconnectTimeoutRef.current);\n                    }\n                }\n            })[\"useWorkflowWebSocket.useEffect\"];\n        }\n    }[\"useWorkflowWebSocket.useEffect\"], [\n        workflowId,\n        autoConnect,\n        connect\n    ]);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useWorkflowWebSocket.useEffect\": ()=>{\n            return ({\n                \"useWorkflowWebSocket.useEffect\": ()=>{\n                    disconnect();\n                }\n            })[\"useWorkflowWebSocket.useEffect\"];\n        }\n    }[\"useWorkflowWebSocket.useEffect\"], [\n        disconnect\n    ]);\n    const actions = {\n        connect,\n        disconnect,\n        sendEvent,\n        clearEvents,\n        reconnect\n    };\n    return [\n        state,\n        actions\n    ];\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useWorkflowWebSocket.ts\n"));

/***/ })

});