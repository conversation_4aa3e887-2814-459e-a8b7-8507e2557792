"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/app/manual-build/[workflowId]/page.tsx":
/*!****************************************************!*\
  !*** ./src/app/manual-build/[workflowId]/page.tsx ***!
  \****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ WorkflowEditorPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @xyflow/react */ \"(app-pages-browser)/./node_modules/@xyflow/system/dist/esm/index.js\");\n/* harmony import */ var _xyflow_react_dist_style_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @xyflow/react/dist/style.css */ \"(app-pages-browser)/./node_modules/@xyflow/react/dist/style.css\");\n/* harmony import */ var _components_manual_build_WorkflowToolbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/manual-build/WorkflowToolbar */ \"(app-pages-browser)/./src/components/manual-build/WorkflowToolbar.tsx\");\n/* harmony import */ var _components_manual_build_NodePalette__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/manual-build/NodePalette */ \"(app-pages-browser)/./src/components/manual-build/NodePalette.tsx\");\n/* harmony import */ var _components_manual_build_NodeConfigPanel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/manual-build/NodeConfigPanel */ \"(app-pages-browser)/./src/components/manual-build/NodeConfigPanel.tsx\");\n/* harmony import */ var _components_manual_build_ContextMenu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/manual-build/ContextMenu */ \"(app-pages-browser)/./src/components/manual-build/ContextMenu.tsx\");\n/* harmony import */ var _components_manual_build_nodes__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/manual-build/nodes */ \"(app-pages-browser)/./src/components/manual-build/nodes/index.ts\");\n/* harmony import */ var _hooks_useWorkflowWebSocket__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/hooks/useWorkflowWebSocket */ \"(app-pages-browser)/./src/hooks/useWorkflowWebSocket.ts\");\n/* harmony import */ var _components_manual_build_ErrorBoundary__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/manual-build/ErrorBoundary */ \"(app-pages-browser)/./src/components/manual-build/ErrorBoundary.tsx\");\n/* harmony import */ var _components_manual_build_ErrorRecoveryPanel__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/manual-build/ErrorRecoveryPanel */ \"(app-pages-browser)/./src/components/manual-build/ErrorRecoveryPanel.tsx\");\n/* harmony import */ var _components_manual_build_WorkflowSharingModal__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/manual-build/WorkflowSharingModal */ \"(app-pages-browser)/./src/components/manual-build/WorkflowSharingModal.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction WorkflowEditorPage(param) {\n    let { params } = param;\n    _s();\n    const resolvedParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const workflowId = resolvedParams === null || resolvedParams === void 0 ? void 0 : resolvedParams.workflowId;\n    const [workflow, setWorkflow] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [nodes, setNodes, onNodesChange] = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_13__.useNodesState)([]);\n    const [edges, setEdges, onEdgesChange] = (0,_xyflow_react__WEBPACK_IMPORTED_MODULE_13__.useEdgesState)([]);\n    const [selectedNode, setSelectedNode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isSaving, setIsSaving] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isDirty, setIsDirty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [contextMenu, setContextMenu] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // WebSocket connection for real-time updates\n    const [wsState, wsActions] = (0,_hooks_useWorkflowWebSocket__WEBPACK_IMPORTED_MODULE_9__.useWorkflowWebSocket)(workflowId !== 'new' ? workflowId : null, {\n        autoConnect: true,\n        onEvent: {\n            \"WorkflowEditorPage.useWorkflowWebSocket\": (event)=>{\n                console.log('Workflow event received:', event);\n                // Handle real-time events here\n                if (event.type === 'workflow_started') {\n                    console.log('🚀 Workflow execution started');\n                } else if (event.type === 'node_started') {\n                    console.log(\"\\uD83D\\uDD04 Node \".concat(event.data.nodeType, \" started\"));\n                } else if (event.type === 'node_completed') {\n                    console.log(\"✅ Node \".concat(event.data.nodeType, \" completed in \").concat(event.data.duration, \"ms\"));\n                } else if (event.type === 'workflow_completed') {\n                    console.log('🎉 Workflow execution completed');\n                } else if (event.type === 'workflow_failed') {\n                    console.error('❌ Workflow execution failed:', event.data.error);\n                }\n            }\n        }[\"WorkflowEditorPage.useWorkflowWebSocket\"],\n        onConnect: {\n            \"WorkflowEditorPage.useWorkflowWebSocket\": ()=>{\n                console.log('🔗 Connected to workflow WebSocket');\n            }\n        }[\"WorkflowEditorPage.useWorkflowWebSocket\"],\n        onDisconnect: {\n            \"WorkflowEditorPage.useWorkflowWebSocket\": ()=>{\n                console.log('🔌 Disconnected from workflow WebSocket');\n            }\n        }[\"WorkflowEditorPage.useWorkflowWebSocket\"],\n        onError: {\n            \"WorkflowEditorPage.useWorkflowWebSocket\": (error)=>{\n                console.error('❌ WebSocket error:', error);\n            }\n        }[\"WorkflowEditorPage.useWorkflowWebSocket\"]\n    });\n    // Error recovery state\n    const [workflowErrors, setWorkflowErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [showErrorPanel, setShowErrorPanel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Sharing state\n    const [showSharingModal, setShowSharingModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load workflow data\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"WorkflowEditorPage.useEffect\": ()=>{\n            if (workflowId === 'new') {\n                initializeNewWorkflow();\n            } else {\n                loadWorkflow(workflowId);\n            }\n        }\n    }[\"WorkflowEditorPage.useEffect\"], [\n        workflowId\n    ]);\n    const initializeNewWorkflow = async ()=>{\n        try {\n            // Create default nodes for new workflow\n            const defaultNodes = [\n                {\n                    id: 'user-request',\n                    type: 'userRequest',\n                    position: {\n                        x: 50,\n                        y: 200\n                    },\n                    data: {\n                        label: 'User Request',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Starting point for user input'\n                    }\n                },\n                {\n                    id: 'classifier',\n                    type: 'classifier',\n                    position: {\n                        x: 350,\n                        y: 200\n                    },\n                    data: {\n                        label: 'Classifier',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Analyzes and categorizes the request'\n                    }\n                },\n                {\n                    id: 'output',\n                    type: 'output',\n                    position: {\n                        x: 950,\n                        y: 200\n                    },\n                    data: {\n                        label: 'Output',\n                        config: {},\n                        isConfigured: true,\n                        description: 'Final response to the user'\n                    }\n                }\n            ];\n            const defaultEdges = [\n                {\n                    id: 'e1',\n                    source: 'user-request',\n                    target: 'classifier',\n                    type: 'smoothstep',\n                    animated: true\n                }\n            ];\n            setNodes(defaultNodes);\n            setEdges(defaultEdges);\n            setIsLoading(false);\n        } catch (error) {\n            console.error('Failed to initialize new workflow:', error);\n            setIsLoading(false);\n        }\n    };\n    const loadWorkflow = async (id)=>{\n        try {\n            // TODO: Implement API call to load workflow\n            console.log('Loading workflow:', id);\n            setIsLoading(false);\n        } catch (error) {\n            console.error('Failed to load workflow:', error);\n            setIsLoading(false);\n        }\n    };\n    const onConnect = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onConnect]\": (params)=>{\n            const newEdge = {\n                ...params,\n                id: \"e\".concat(edges.length + 1),\n                type: 'smoothstep',\n                animated: true\n            };\n            setEdges({\n                \"WorkflowEditorPage.useCallback[onConnect]\": (eds)=>(0,_xyflow_react__WEBPACK_IMPORTED_MODULE_14__.addEdge)(newEdge, eds)\n            }[\"WorkflowEditorPage.useCallback[onConnect]\"]);\n            setIsDirty(true);\n        }\n    }[\"WorkflowEditorPage.useCallback[onConnect]\"], [\n        edges.length,\n        setEdges\n    ]);\n    const onNodeClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onNodeClick]\": (event, node)=>{\n            setSelectedNode(node);\n        }\n    }[\"WorkflowEditorPage.useCallback[onNodeClick]\"], []);\n    const onPaneClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onPaneClick]\": ()=>{\n            setSelectedNode(null);\n            setContextMenu(null);\n        }\n    }[\"WorkflowEditorPage.useCallback[onPaneClick]\"], []);\n    const onNodeContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onNodeContextMenu]\": (event, node)=>{\n            event.preventDefault();\n            setContextMenu({\n                id: node.id,\n                type: 'node',\n                nodeType: node.type,\n                x: event.clientX,\n                y: event.clientY\n            });\n        }\n    }[\"WorkflowEditorPage.useCallback[onNodeContextMenu]\"], []);\n    const onEdgeContextMenu = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[onEdgeContextMenu]\": (event, edge)=>{\n            event.preventDefault();\n            setContextMenu({\n                id: edge.id,\n                type: 'edge',\n                x: event.clientX,\n                y: event.clientY\n            });\n        }\n    }[\"WorkflowEditorPage.useCallback[onEdgeContextMenu]\"], []);\n    const handleDeleteNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (nodeId)=>{\n            // Don't delete core nodes\n            const coreNodes = [\n                'user-request',\n                'classifier',\n                'output'\n            ];\n            if (coreNodes.includes(nodeId)) return;\n            setNodes({\n                \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (nds)=>nds.filter({\n                        \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (node)=>node.id !== nodeId\n                    }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"])\n            }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"]);\n            setEdges({\n                \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (eds)=>eds.filter({\n                        \"WorkflowEditorPage.useCallback[handleDeleteNode]\": (edge)=>edge.source !== nodeId && edge.target !== nodeId\n                    }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"])\n            }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"]);\n            setIsDirty(true);\n            // Close config panel if deleted node was selected\n            if ((selectedNode === null || selectedNode === void 0 ? void 0 : selectedNode.id) === nodeId) {\n                setSelectedNode(null);\n            }\n        }\n    }[\"WorkflowEditorPage.useCallback[handleDeleteNode]\"], [\n        selectedNode,\n        setNodes,\n        setEdges\n    ]);\n    const handleDeleteEdge = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[handleDeleteEdge]\": (edgeId)=>{\n            setEdges({\n                \"WorkflowEditorPage.useCallback[handleDeleteEdge]\": (eds)=>eds.filter({\n                        \"WorkflowEditorPage.useCallback[handleDeleteEdge]\": (edge)=>edge.id !== edgeId\n                    }[\"WorkflowEditorPage.useCallback[handleDeleteEdge]\"])\n            }[\"WorkflowEditorPage.useCallback[handleDeleteEdge]\"]);\n            setIsDirty(true);\n        }\n    }[\"WorkflowEditorPage.useCallback[handleDeleteEdge]\"], [\n        setEdges\n    ]);\n    const handleDuplicateNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[handleDuplicateNode]\": (nodeId)=>{\n            const nodeToDuplicate = nodes.find({\n                \"WorkflowEditorPage.useCallback[handleDuplicateNode].nodeToDuplicate\": (n)=>n.id === nodeId\n            }[\"WorkflowEditorPage.useCallback[handleDuplicateNode].nodeToDuplicate\"]);\n            if (!nodeToDuplicate) return;\n            const newNode = {\n                ...nodeToDuplicate,\n                id: \"\".concat(nodeToDuplicate.type, \"-\").concat(Date.now()),\n                position: {\n                    x: nodeToDuplicate.position.x + 50,\n                    y: nodeToDuplicate.position.y + 50\n                },\n                data: {\n                    ...nodeToDuplicate.data,\n                    label: \"\".concat(nodeToDuplicate.data.label, \" Copy\")\n                }\n            };\n            setNodes({\n                \"WorkflowEditorPage.useCallback[handleDuplicateNode]\": (nds)=>[\n                        ...nds,\n                        newNode\n                    ]\n            }[\"WorkflowEditorPage.useCallback[handleDuplicateNode]\"]);\n            setIsDirty(true);\n        }\n    }[\"WorkflowEditorPage.useCallback[handleDuplicateNode]\"], [\n        nodes,\n        setNodes\n    ]);\n    const handleConfigureNode = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"WorkflowEditorPage.useCallback[handleConfigureNode]\": (nodeId)=>{\n            const node = nodes.find({\n                \"WorkflowEditorPage.useCallback[handleConfigureNode].node\": (n)=>n.id === nodeId\n            }[\"WorkflowEditorPage.useCallback[handleConfigureNode].node\"]);\n            if (node) {\n                setSelectedNode(node);\n            }\n        }\n    }[\"WorkflowEditorPage.useCallback[handleConfigureNode]\"], [\n        nodes\n    ]);\n    const handleSave = async ()=>{\n        if (!workflow && workflowId === 'new') {\n            // Show save dialog for new workflow\n            const name = prompt('Enter workflow name:');\n            if (!name) return;\n            const description = prompt('Enter workflow description (optional):') || '';\n            setIsSaving(true);\n            try {\n                const response = await fetch('/api/workflows', {\n                    method: 'POST',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        name,\n                        description,\n                        nodes,\n                        edges,\n                        settings: {}\n                    })\n                });\n                if (!response.ok) {\n                    const errorData = await response.json();\n                    throw new Error(errorData.details || 'Failed to save workflow');\n                }\n                const result = await response.json();\n                // Show API key to user (only shown once!)\n                alert(\"Workflow saved successfully!\\n\\nYour API Key: \".concat(result.api_key, \"\\n\\nSave this key - it will not be shown again!\"));\n                // Redirect to the saved workflow\n                router.push(\"/manual-build/\".concat(result.workflow.id));\n            } catch (error) {\n                console.error('Failed to save workflow:', error);\n                alert(\"Failed to save workflow: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n            } finally{\n                setIsSaving(false);\n            }\n        } else {\n            // Update existing workflow\n            setIsSaving(true);\n            try {\n                const response = await fetch('/api/workflows', {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        id: workflowId,\n                        name: workflow === null || workflow === void 0 ? void 0 : workflow.name,\n                        description: workflow === null || workflow === void 0 ? void 0 : workflow.description,\n                        nodes,\n                        edges,\n                        settings: (workflow === null || workflow === void 0 ? void 0 : workflow.settings) || {}\n                    })\n                });\n                if (!response.ok) {\n                    const errorData = await response.json();\n                    throw new Error(errorData.details || 'Failed to update workflow');\n                }\n                setIsDirty(false);\n                alert('Workflow updated successfully!');\n            } catch (error) {\n                console.error('Failed to update workflow:', error);\n                alert(\"Failed to update workflow: \".concat(error instanceof Error ? error.message : 'Unknown error'));\n            } finally{\n                setIsSaving(false);\n            }\n        }\n    };\n    const handleTestInPlayground = ()=>{\n        if (workflow === null || workflow === void 0 ? void 0 : workflow.id) {\n            window.open(\"/playground/workflows\", '_blank');\n        } else {\n            alert('Please save the workflow first to test it in the playground');\n        }\n    };\n    const handleAddNode = (nodeType, position)=>{\n        let defaultConfig = {};\n        let isConfigured = true;\n        // Set proper default config for specific node types\n        if (nodeType === 'provider') {\n            defaultConfig = {\n                providerId: '',\n                modelId: '',\n                apiKey: '',\n                parameters: {\n                    temperature: 1.0,\n                    maxTokens: undefined,\n                    topP: undefined,\n                    frequencyPenalty: undefined,\n                    presencePenalty: undefined\n                }\n            };\n            isConfigured = false;\n        } else if (nodeType === 'centralRouter') {\n            defaultConfig = {\n                routingStrategy: 'smart',\n                fallbackProvider: '',\n                maxRetries: 3,\n                timeout: 30000,\n                enableCaching: true,\n                debugMode: false\n            };\n            isConfigured = true;\n        }\n        const newNode = {\n            id: \"\".concat(nodeType, \"-\").concat(Date.now()),\n            type: nodeType,\n            position,\n            data: {\n                label: nodeType === 'centralRouter' ? 'Central Router' : nodeType.charAt(0).toUpperCase() + nodeType.slice(1),\n                config: defaultConfig,\n                isConfigured,\n                description: \"\".concat(nodeType, \" node\")\n            }\n        };\n        setNodes((nds)=>[\n                ...nds,\n                newNode\n            ]);\n        setIsDirty(true);\n    };\n    const handleNodeUpdate = (nodeId, updates)=>{\n        setNodes((nds)=>nds.map((node)=>node.id === nodeId ? {\n                    ...node,\n                    data: {\n                        ...node.data,\n                        ...updates\n                    }\n                } : node));\n        setIsDirty(true);\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen bg-[#040716] flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-white\",\n                children: \"Loading workflow...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                lineNumber: 400,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n            lineNumber: 399,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_ErrorBoundary__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n        showDetails: \"development\" === 'development',\n        onError: (error, errorInfo)=>{\n            console.error('Workflow Editor Error:', error, errorInfo);\n            // Add to error list for recovery panel\n            const newError = {\n                id: \"error-\".concat(Date.now()),\n                nodeId: 'editor',\n                nodeType: 'editor',\n                nodeLabel: 'Workflow Editor',\n                message: error.message,\n                timestamp: new Date().toISOString(),\n                attempt: 1,\n                maxRetries: 3,\n                status: 'pending',\n                recoveryStrategies: [\n                    {\n                        type: 'retry',\n                        description: 'Reload the editor',\n                        available: true,\n                        recommended: true\n                    }\n                ]\n            };\n            setWorkflowErrors((prev)=>[\n                    ...prev,\n                    newError\n                ]);\n            setShowErrorPanel(true);\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"h-screen bg-[#040716] flex flex-col\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_WorkflowToolbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    workflow: workflow,\n                    isDirty: isDirty,\n                    isSaving: isSaving,\n                    onSave: handleSave,\n                    onExecute: handleTestInPlayground,\n                    onBack: ()=>router.push('/manual-build'),\n                    onShare: ()=>setShowSharingModal(true)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                    lineNumber: 436,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 flex\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_NodePalette__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            onAddNode: handleAddNode\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                            lineNumber: 448,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative manual-build-canvas\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_13__.ReactFlow, {\n                                    nodes: nodes,\n                                    edges: edges,\n                                    onNodesChange: onNodesChange,\n                                    onEdgesChange: onEdgesChange,\n                                    onConnect: onConnect,\n                                    onNodeClick: onNodeClick,\n                                    onNodeContextMenu: onNodeContextMenu,\n                                    onEdgeContextMenu: onEdgeContextMenu,\n                                    onPaneClick: onPaneClick,\n                                    nodeTypes: _components_manual_build_nodes__WEBPACK_IMPORTED_MODULE_8__.nodeTypes,\n                                    fitView: true,\n                                    className: \"bg-[#040716]\",\n                                    defaultViewport: {\n                                        x: 0,\n                                        y: 0,\n                                        zoom: 0.8\n                                    },\n                                    connectionLineStyle: {\n                                        stroke: '#ff6b35',\n                                        strokeWidth: 2\n                                    },\n                                    defaultEdgeOptions: {\n                                        style: {\n                                            stroke: '#ff6b35',\n                                            strokeWidth: 2\n                                        },\n                                        type: 'smoothstep',\n                                        animated: true\n                                    },\n                                    proOptions: {\n                                        hideAttribution: true\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_13__.Background, {\n                                            color: \"#1f2937\",\n                                            gap: 20,\n                                            size: 1\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                            lineNumber: 474,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_13__.Controls, {\n                                            className: \"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm\",\n                                            showInteractive: false\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_xyflow_react__WEBPACK_IMPORTED_MODULE_13__.MiniMap, {\n                                            className: \"bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm\",\n                                            nodeColor: \"#ff6b35\",\n                                            maskColor: \"rgba(0, 0, 0, 0.2)\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                            lineNumber: 483,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                    lineNumber: 452,\n                                    columnNumber: 13\n                                }, this),\n                                contextMenu && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_ContextMenu__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    id: contextMenu.id,\n                                    type: contextMenu.type,\n                                    nodeType: contextMenu.nodeType,\n                                    top: contextMenu.y,\n                                    left: contextMenu.x,\n                                    onClose: ()=>setContextMenu(null),\n                                    onDelete: contextMenu.type === 'node' ? handleDeleteNode : handleDeleteEdge,\n                                    onDuplicate: contextMenu.type === 'node' ? handleDuplicateNode : undefined,\n                                    onConfigure: contextMenu.type === 'node' ? handleConfigureNode : undefined,\n                                    onDisconnect: contextMenu.type === 'edge' ? handleDeleteEdge : undefined\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                                    lineNumber: 492,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                            lineNumber: 451,\n                            columnNumber: 11\n                        }, this),\n                        selectedNode && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_NodeConfigPanel__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            node: selectedNode,\n                            onUpdate: (updates)=>handleNodeUpdate(selectedNode.id, updates),\n                            onClose: ()=>setSelectedNode(null)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                            lineNumber: 509,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                    lineNumber: 446,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_ErrorRecoveryPanel__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    errors: workflowErrors,\n                    onRetry: (errorId)=>{\n                        console.log('Retrying error:', errorId);\n                    // Implement retry logic\n                    },\n                    onSkip: (errorId)=>{\n                        console.log('Skipping error:', errorId);\n                    // Implement skip logic\n                    },\n                    onManualFix: (errorId)=>{\n                        console.log('Manual fix for error:', errorId);\n                    // Implement manual fix logic\n                    },\n                    isVisible: showErrorPanel,\n                    onClose: ()=>setShowErrorPanel(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                    lineNumber: 518,\n                    columnNumber: 9\n                }, this),\n                workflow && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_manual_build_WorkflowSharingModal__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    workflowId: workflow.id,\n                    workflowName: workflow.name,\n                    isOpen: showSharingModal,\n                    onClose: ()=>setShowSharingModal(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n                    lineNumber: 538,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n            lineNumber: 434,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\manual-build\\\\[workflowId]\\\\page.tsx\",\n        lineNumber: 406,\n        columnNumber: 5\n    }, this);\n}\n_s(WorkflowEditorPage, \"huk/Bt72SGAXJf0bpcw63BW/RCg=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_13__.useNodesState,\n        _xyflow_react__WEBPACK_IMPORTED_MODULE_13__.useEdgesState,\n        _hooks_useWorkflowWebSocket__WEBPACK_IMPORTED_MODULE_9__.useWorkflowWebSocket\n    ];\n});\n_c = WorkflowEditorPage;\nvar _c;\n$RefreshReg$(_c, \"WorkflowEditorPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/manual-build/[workflowId]/page.tsx\n"));

/***/ })

});